import { APISpec } from '@mfe/cc-api-caller-bee';

export const enum MessageType {
    SYSTEM = -1,
    QUESTION = 1,
    ANSWER,
}

export const enum MessageContentType {
    WELCOME = -1,
    TEXT = 1,
    WITH_OPTIONS,
}

// 小于0的，都是本地定义的状态
export const enum MessageStatus {
    STOPPED = -11, // 本地定义的状态, 用户主动停止
    ERROR = -10, // 本地定义的状态, 消息轮询失败

    DONE_AFTER_STOP = -3, // 本地定义的状态, 用户点击停止后的状态
    TYPING = -2, // 本地定义的状态, 正在动画输出中
    TO_GENERATE = -1, // 本地定义的状态, 用户刚发完消息，后端还没返回前的状态
    GENERATING = 0,
    DONE,
}

export const enum AbilityType {
    GENERAL = 1,
    JUMP,
    SERVICE_SCORE,
    WELCOME,
}
export type MessageSendReq =
    APISpec['/bee/v1/bdaiassistant/submitQuery']['request'] & {
        entryPointType?: EntryPointType;
    };

export type Message = Omit<
    APISpec['/bee/v1/bdaiassistant/fetchAnswer']['response'],
    'previousContent'
> & {
    history?: boolean;
    lastStatus?: MessageStatus;
    req?: MessageSendReq;
    entryPointType?: EntryPointType;
    isQuestion?: boolean;
    previousContent?: any[]; // previousContent后端不再返回
};

export const enum FeedbackType {
    LIKE = 1,
    DISLIKE,
    BLOCK_ANSWER,
    TT,
    COPY,
}

export const enum SelectionOptionType {
    LINK = 1,
    MESSAGE,
}

export enum EntryPointType {
    USER = 1,
    VOICE,
    ASSOCIATION,
    TOOL,
    WELCOME,
    SECTION,
    POI_SELECTOR, // 商家选择器
    REJECT_SELECTOR, // 驳回原因选择器
}
export enum EntryPoint {
    tab = 'tab',
    option_list = 'option_list',
    input = 'input',
    toolbar = 'toolbar',
    poi_select = 'poi_select',
    input_prediction = 'input_prediction',
    voice_input = 'voice_input',
    poi_reject_selector = 'poi_reject_selector',
    refresh = 'refresh',
    form_input = 'form_input',
    action_card = 'action_card',
}

export enum SOURCE {
    mine = 'bee-mine',
    home = 'bee-home',
    workbench = 'bee-workbench',
}
